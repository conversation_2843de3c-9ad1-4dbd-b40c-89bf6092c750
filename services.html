<!DOCTYPE html>
<html lang="en">
<head>
  <meta name="description" content="Zeta Technologies delivers next-gen AI solutions for social media and digital transformation. Discover NeTuArk and RoBERTo.">
<meta name="robots" content="index, follow">
<meta name="author" content="Zeta Technologies">
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Zeta Technologies - Our Services</title>
  <!-- Modern fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=DM+Sans:wght@400;500;700&display=swap" rel="stylesheet">
  <!-- Tailwind CSS -->
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <!-- Three.js -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
  <style>
    :root {
      --primary: #6366f1;
      --primary-dark: #4f46e5;
      --secondary: #f59e0b;
      --dark: #111827;
      --light: #f5f3ff;
    }

    body {
      font-family: 'DM Sans', sans-serif;
      background-color: #080808;
      color: #ffffff;
      user-select: none;
      overflow-x: hidden;
    }

    .heading-font {
      font-family: 'Space Grotesk', sans-serif;
      font-weight: 600;
    }

    /* Glassmorphism card effect */
    .glass-card {
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 1rem;
      transition: all 0.4s ease;
    }

    .glass-card:hover {
      transform: translateY(-10px);
      background: rgba(255, 255, 255, 0.1);
      box-shadow: 0 10px 30px rgba(99, 102, 241, 0.2);
    }

    /* Gradient text */
    .gradient-text {
      background: linear-gradient(90deg, #6366f1, #8b5cf6, #d946ef);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-size: 300% 100%;
      animation: gradient-shift 8s ease infinite;
    }

    @keyframes gradient-shift {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }

    /* 3D canvas */
    #three-canvas {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100vh;
      z-index: -1;
    }

    /* Navigation Menu */
    .nav-link {
      position: relative;
      padding: 0.5rem 0;
      margin: 0 1rem;
      transition: all 0.3s ease;
    }

    .nav-link::after {
      content: '';
      position: absolute;
      width: 0;
      height: 2px;
      bottom: 0;
      left: 0;
      background-color: var(--primary);
      transition: width 0.3s ease;
    }

    .nav-link:hover::after {
      width: 100%;
    }

    /* Mobile menu */
    .mobile-menu {
      position: fixed;
      top: 0;
      right: -100%;
      width: 80%;
      height: 100%;
      background: rgba(0, 0, 0, 0.95);
      backdrop-filter: blur(10px);
      transition: right 0.4s cubic-bezier(0.77, 0, 0.175, 1);
      z-index: 20;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }

    .mobile-menu.open {
      right: 0;
    }

    /* Overlay for mobile menu */
    .menu-overlay {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 15;
    }

    .menu-overlay.show {
      display: block;
    }

    /* Service number badge */
    .number-badge {
      background: linear-gradient(135deg, #f59e0b 0%, #fb923c 100%);
      box-shadow: 0 4px 10px rgba(245, 158, 11, 0.3);
    }

    /* Service card hover effects */
    .service-card {
      transition: all 0.4s ease;
      overflow: hidden;
    }

    .service-card:hover {
      transform: translateY(-10px) scale(1.02);
    }

    .service-card::before {
      content: '';
      position: absolute;
      top: -100%;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, rgba(99, 102, 241, 0.3) 0%, rgba(99, 102, 241, 0) 100%);
      transition: all 0.6s ease;
      z-index: -1;
    }

    .service-card:hover::before {
      top: 0;
      left: 0;
    }
  </style>
</head>
<body class="min-h-screen" oncontextmenu="return false" oncopy="return false" onpaste="return false">
  <!-- Three.js Canvas -->
  <div id="three-canvas"></div>

  <!-- Navigation Menu -->
  <nav class="container mx-auto px-6 py-6 relative z-10">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-3">
        <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
          <img src="Screenshot_20250104-002946-removebg-preview.png" alt="Zeta Technologies AI Platform Overview">
        </div>
        <span class="heading-font text-2xl gradient-text">Zeta Technologies</span>
      </div>

      <!-- Hamburger Menu for Mobile -->
      <div class="md:hidden flex items-center">
        <div class="hamburger text-2xl text-white" onclick="toggleMenu()">
          <i class="fas fa-bars"></i>
        </div>
      </div>

      <!-- Desktop Navigation (visible on medium+ screens) -->      <div class="hidden md:flex items-center space-x-4">
        <a href="index.html" class="nav-link text-gray-300 hover:text-white">Home</a>
        <a href="products.html" class="nav-link text-gray-300 hover:text-white">Products</a>
        <a href="services.html" class="nav-link text-gray-300 hover:text-white">Services</a>
        <a href="work.html" class="nav-link text-gray-300 hover:text-white">Our Work</a>
        <a href="about.html" class="nav-link text-gray-300 hover:text-white">About Us</a>
        <a href="careers.html" class="nav-link text-gray-300 hover:text-white">Careers</a>
        <a href="https://ko-fi.com/ZetaTech" target="_blank" class="ml-2 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-4 py-2 rounded-lg transition duration-300 flex items-center space-x-2">
          <i class="fas fa-mug-hot"></i>
          <span class="hidden lg:inline">Support Us</span>
        </a>
        <button onclick="toggleFullScreen()" class="ml-4 bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition duration-300 flex items-center space-x-2">
          <i class="fas fa-expand"></i>
          <span class="hidden lg:inline">Fullscreen</span>
        </button>
      </div>
    </div>
  </nav>

  <!-- Mobile Menu -->
  <div id="menuOverlay" class="menu-overlay" onclick="toggleMenu()"></div>
  <div id="mobileMenu" class="mobile-menu">
    <div class="absolute top-6 right-6 text-2xl text-white" onclick="toggleMenu()">
      <i class="fas fa-times"></i>
    </div>    <div class="flex flex-col items-center space-y-8">
      <a href="index.html" class="nav-link text-gray-300 hover:text-white">Home</a>
      <a href="products.html" class="nav-link text-gray-300 hover:text-white">Products</a>
      <a href="services.html" class="nav-link text-gray-300 hover:text-white">Services</a>
      <a href="work.html" class="nav-link text-gray-300 hover:text-white">Our Work</a>
      <a href="about.html" class="nav-link text-gray-300 hover:text-white">About Us</a>
      <a href="careers.html" class="nav-link text-gray-300 hover:text-white">Careers</a>
      <a href="https://ko-fi.com/ZetaTech" target="_blank" class="mt-6 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-6 py-3 rounded-lg transition duration-300 flex items-center space-x-2">
        <i class="fas fa-mug-hot"></i>
        <span>Support Us</span>
      </a>
      <button onclick="toggleFullScreen()" class="mt-6 bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg transition duration-300 flex items-center space-x-2">
        <i class="fas fa-expand"></i>
        <span>Fullscreen</span>
      </button>
    </div>
  </div>

  <!-- Main Content -->
  <main class="container mx-auto px-6 py-12 relative z-10">
    <header class="mb-16 text-center">
      <h1 class="heading-font text-5xl md:text-6xl gradient-text mb-4">Our Services</h1>
      <p class="text-gray-400 text-xl">Tailored solutions to meet your digital needs</p>
    </header>

    <!-- Services Section -->
    <section class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
      <!-- Service Card 1: Graphic Design -->
      <div class="relative service-card glass-card p-8 cursor-default">
        <div class="absolute -top-4 -left-4 md:-left-2 number-badge w-10 h-10 rounded-full flex items-center justify-center text-white font-bold z-10">1</div>
        <div class="flex items-center mb-4">
          <span class="text-3xl text-purple-400 mr-3"><i class="fas fa-palette"></i></span>
          <h3 class="heading-font text-2xl font-bold text-white">Graphic Design Services</h3>
        </div>
        <p class="text-gray-400 text-lg">
          Crafting visually stunning designs across digital and print media—from social posts and infographics to brochures and brand assets—to elevate your message and captivate your audience.
        </p>
      </div>
      <div class="relative service-card glass-card p-8 cursor-default">
        <div class="absolute -top-4 -left-4 md:-left-2 number-badge w-10 h-10 rounded-full flex items-center justify-center text-white font-bold z-10">2</div>
        <div class="flex items-center mb-4">
          <span class="text-3xl text-blue-400 mr-3"><i class="fas fa-desktop"></i></span>
          <h3 class="heading-font text-2xl font-bold text-white">UI Design</h3>
        </div>
        <p class="text-gray-400 text-lg">Crafting visually appealing and user-friendly interfaces tailored to your brand with attention to detail and user experience.</p>
      </div>

      <!-- Service Card 3: Front End Development -->
      <div class="relative service-card glass-card p-8 cursor-default">
        <div class="absolute -top-4 -left-4 md:-left-2 number-badge w-10 h-10 rounded-full flex items-center justify-center text-white font-bold z-10">3</div>
        <div class="flex items-center mb-4">
          <span class="text-3xl text-green-400 mr-3"><i class="fas fa-code"></i></span>
          <h3 class="heading-font text-2xl font-bold text-white">Front End Development</h3>
        </div>
        <p class="text-gray-400 text-lg">Building interactive and responsive web interfaces using modern technologies like React, Vue, and Angular.</p>
      </div>

      <!-- Service Card 4: Full Stack Development -->
      <div class="relative service-card glass-card p-8 cursor-default">
        <div class="absolute -top-4 -left-4 md:-left-2 number-badge w-10 h-10 rounded-full flex items-center justify-center text-white font-bold z-10">4</div>
        <div class="flex items-center mb-4">
          <span class="text-3xl text-indigo-400 mr-3"><i class="fas fa-server"></i></span>
          <h3 class="heading-font text-2xl font-bold text-white">Full Stack Development</h3>
        </div>
        <p class="text-gray-400 text-lg">Offering comprehensive development services from front end to back end with database integration and API development.</p>
      </div>

      <!-- Service Card 5: Image Editing -->
      <div class="relative service-card glass-card p-8 cursor-default">
        <div class="absolute -top-4 -left-4 md:-left-2 number-badge w-10 h-10 rounded-full flex items-center justify-center text-white font-bold z-10">5</div>
        <div class="flex items-center mb-4">
          <span class="text-3xl text-pink-400 mr-3"><i class="fas fa-image"></i></span>
          <h3 class="heading-font text-2xl font-bold text-white">Image Editing</h3>
        </div>
        <p class="text-gray-400 text-lg">Professional photo retouching, color correction, and creative image manipulation for all your visual needs.</p>
      </div>

      <!-- Service Card 6: Logo Creation -->
      <div class="relative service-card glass-card p-8 cursor-default">
  <div class="absolute -top-4 -left-4 md:-left-2 number-badge w-10 h-10 rounded-full flex items-center justify-center text-white font-bold z-10">6</div>
  <h3 class="heading-font text-2xl font-bold text-white mb-2">Logo Creation</h3>
  <p class="text-gray-400 text-lg">Crafting unique, memorable logos that capture your brand’s identity and leave a lasting impression.</p>
      </div>    </section>

    <!-- Link to Work Page -->
    <section class="mt-16 text-center">
      <a href="work.html" class="inline-block bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white px-8 py-4 rounded-lg transition duration-300 text-lg font-semibold">
        <i class="fas fa-briefcase mr-2"></i>
        View Our Work Portfolio
      </a>
      <p class="text-gray-400 mt-4">Check out our team's latest projects and achievements</p>
    </section>

    <!-- Contact Button Section -->
    <section class="mt-16 text-center">
      <a href="mailto:<EMAIL>" class="bg-indigo-600 hover:bg-indigo-700 text-white px-8 py-3 rounded-full transition duration-300 inline-flex items-center space-x-2">
        <i class="fas fa-envelope"></i>
        <span>Contact Us</span>
      </a>
      <a href="https://ko-fi.com/ZetaTech" target="_blank" class="ml-4 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-3 rounded-full transition duration-300 inline-flex items-center space-x-2">
        <i class="fas fa-mug-hot"></i>
        <span>Support Our Work</span>
      </a>
    </section>

    <!-- Footer -->
    <footer class="mt-32 pb-12">
      <div class="max-w-4xl mx-auto glass-card p-8">
        <div class="flex flex-col md:flex-row justify-between items-center">
          <div class="mb-8 md:mb-0">
            <div class="flex items-center space-x-3 mb-4">
              <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                <img src="Screenshot_20250104-002946-removebg-preview.png" alt="Zeta Technologies AI Platform Overview">
              </div>
              <span class="heading-font text-xl gradient-text">Zeta Technologies</span>
            </div>
            <p class="text-gray-400">Innovation Without Compromise</p>
          </div>
          <div class="flex space-x-6">
            <a href="https://x.com/TechAtZeta?t=f5UsigfHtK2gUjF5zQhpPQ&s=09" target="_blank" class="text-gray-400 hover:text-white transition">
              <i class="fab fa-twitter text-xl"></i>
            </a>
            <a href="https://web.facebook.com/profile.php?id=61576226673602" target="_blank" class="text-gray-400 hover:text-white transition">
              <i class="fab fa-facebook text-xl"></i>
            </a>
            <a href="https://www.linkedin.com/company/phase-official/" target="_blank" class="text-gray-400 hover:text-white transition">
              <i class="fab fa-linkedin text-xl"></i>
            </a>
            <a href="https://www.instagram.com/zetatechhq/" target="_blank" class="text-gray-400 hover:text-white transition">
              <i class="fab fa-instagram text-xl"></i>
            </a>
            <a href="https://ko-fi.com/ZetaTech" target="_blank" class="text-blue-400 hover:text-blue-300 transition">
              <i class="fas fa-mug-hot text-xl"></i>
            </a>
          </div>
        </div>
        <div class="mt-8 pt-8 border-t border-gray-800 text-center text-gray-500">
          &copy; 2025 Zeta Technologies. All rights reserved.
        </div>
      </div>
    </footer>
  </main>

  <!-- JavaScript for menu toggle and fullscreen -->
    <script>
      function toggleMenu() {
        const menu = document.getElementById('mobileMenu');
        const overlay = document.getElementById('menuOverlay');
        menu.classList.toggle('open');
        overlay.classList.toggle('show');
      }

      function toggleFullScreen() {
        if (!document.fullscreenElement && !document.mozFullScreenElement && !document.webkitFullscreenElement) {
          if (document.documentElement.requestFullscreen) {
            document.documentElement.requestFullscreen();
          } else if (document.documentElement.mozRequestFullScreen) {
            document.documentElement.mozRequestFullScreen();
          } else if (document.documentElement.webkitRequestFullscreen) {
            document.documentElement.webkitRequestFullscreen();
          } else if (document.documentElement.msRequestFullscreen) {
            document.documentElement.msRequestFullscreen();
          }
        } else {
          if (document.exitFullscreen) {
            document.exitFullscreen();
          } else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen();
          } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
          } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
          }
        }
      }

      // Three.js Animation
      const scene = new THREE.Scene();
      const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
      const renderer = new THREE.WebGLRenderer({ alpha: true });
      renderer.setSize(window.innerWidth, window.innerHeight);
      document.getElementById('three-canvas').appendChild(renderer.domElement);

      const geometry = new THREE.TorusKnotGeometry(10, 3, 100, 16);
      const material = new THREE.MeshBasicMaterial({
        color: 0x6366f1,
        wireframe: true,
        transparent: true,
        opacity: 0.1
      });
      const torusKnot = new THREE.Mesh(geometry, material);
      scene.add(torusKnot);

      camera.position.z = 30;

      function animate() {
        requestAnimationFrame(animate);
        torusKnot.rotation.x += 0.01;
        torusKnot.rotation.y += 0.01;
        renderer.render(scene, camera);
      }

      // Handle window resize
      window.addEventListener('resize', () => {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
      });

      animate();
    </script>
  </body>
</html>
