<!DOCTYPE html>
<html lang="en">
<head>
  <meta name="description" content="Xerv delivers next-gen AI solutions for social media and digital transformation. Discover NeTuArk and RoBERTo.">
<meta name="robots" content="index, follow">
<meta name="author" content="Xerv">
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Xerv - About Us</title>
  <!-- Modern fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=DM+Sans:wght@400;500;700&display=swap" rel="stylesheet">
  <!-- Tailwind CSS -->
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <!-- Three.js -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
  <style>
    :root {
      --primary: #A3E635;
      --primary-dark: #84CC16;
      --secondary: #22C55E;
      --dark: #111827;
      --light: #f5f3ff;
    }

    body {
      font-family: 'DM Sans', sans-serif;
      background-color: #080808;
      color: #ffffff;
      user-select: none;
      overflow-x: hidden;
    }

    .heading-font {
      font-family: 'Space Grotesk', sans-serif;
      font-weight: 600;
    }

    /* Glassmorphism card effect */
    .glass-card {
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 1rem;
      transition: all 0.4s ease;
    }

    .glass-card:hover {
      transform: translateY(-10px);
      background: rgba(255, 255, 255, 0.1);
      box-shadow: 0 10px 30px rgba(99, 102, 241, 0.2);
    }

    /* Gradient text */
    .gradient-text {
      background: linear-gradient(90deg, #A3E635, #22C55E, #10B981);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-size: 300% 100%;
      animation: gradient-shift 8s ease infinite;
    }

    @keyframes gradient-shift {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }

    /* 3D canvas */
    #three-canvas {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100vh;
      z-index: -1;
    }

    /* Navigation Menu */
    .nav-link {
      position: relative;
      padding: 0.5rem 0;
      margin: 0 1rem;
      transition: all 0.3s ease;
    }

    .nav-link::after {
      content: '';
      position: absolute;
      width: 0;
      height: 2px;
      bottom: 0;
      left: 0;
      background-color: var(--primary);
      transition: width 0.3s ease;
    }

    .nav-link:hover::after {
      width: 100%;
    }

    /* Mobile menu */
    .mobile-menu {
      position: fixed;
      top: 0;
      right: -100%;
      width: 80%;
      height: 100%;
      background: rgba(0, 0, 0, 0.95);
      backdrop-filter: blur(10px);
      transition: right 0.4s cubic-bezier(0.77, 0, 0.175, 1);
      z-index: 20;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }

    .mobile-menu.open {
      right: 0;
    }

    /* Overlay for mobile menu */
    .menu-overlay {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 15;
    }

    .menu-overlay.show {
      display: block;
    }

    /* Content card styles */
    .content-card {
      padding: 2rem;
    }
  </style>
</head>
<body class="min-h-screen" oncontextmenu="return false" oncopy="return false" onpaste="return false">
  <!-- Three.js Canvas -->
  <div id="three-canvas"></div>

  <!-- Navigation Menu -->
  <nav class="container mx-auto px-6 py-6 relative z-10">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-3">
        <div class="w-12 h-12 bg-gradient-to-br from-lime-500 to-green-600 rounded-lg flex items-center justify-center">
          <span class="text-white text-xl font-bold">X</span>
        </div>
        <span class="heading-font text-2xl gradient-text">Xerv</span>
      </div>

      <!-- Hamburger Menu for Mobile -->
      <div class="md:hidden flex items-center">
        <div class="hamburger text-2xl text-white" onclick="toggleMenu()">
          <i class="fas fa-bars"></i>
        </div>
      </div>

      <!-- Desktop Navigation (visible on medium+ screens) -->
      <div class="hidden md:flex items-center space-x-4">
        <a href="index.html" class="nav-link text-gray-300 hover:text-white">Home</a>
                <a href="products.html" class="nav-link text-gray-300 hover:text-white">Products</a>
                <a href="services.html" class="nav-link text-gray-300 hover:text-white">Services</a>
                <a href="about.html" class="nav-link text-gray-300 hover:text-white">About Us</a>
                <a href="careers.html" class="nav-link text-gray-300 hover:text-white">Careers</a>
                <a href="mailto:<EMAIL>" class="ml-2 bg-gradient-to-r from-lime-500 to-green-600 hover:from-lime-600 hover:to-green-700 text-white px-4 py-2 rounded-lg transition duration-300 flex items-center space-x-2">
                  <i class="fas fa-envelope"></i>
                  <span class="hidden lg:inline">Contact</span>
                </a>
                <button onclick="toggleFullScreen()" class="ml-4 bg-lime-600 hover:bg-lime-700 text-white px-4 py-2 rounded-lg transition duration-300 flex items-center space-x-2">
                  <i class="fas fa-expand"></i>
                  <span class="hidden lg:inline">Fullscreen</span>
                </button>
      </div>
    </div>
  </nav>

  <!-- Mobile Menu -->
  <div id="menuOverlay" class="menu-overlay" onclick="toggleMenu()"></div>
  <div id="mobileMenu" class="mobile-menu">
    <div class="absolute top-6 right-6 text-2xl text-white" onclick="toggleMenu()">
      <i class="fas fa-times"></i>
    </div>
    <div class="flex flex-col items-center space-y-8">
      <a href="index.html" class="nav-link text-gray-300 hover:text-white">Home</a>
                <a href="products.html" class="nav-link text-gray-300 hover:text-white">Products</a>
                <a href="services.html" class="nav-link text-gray-300 hover:text-white">Services</a>
                <a href="about.html" class="nav-link text-gray-300 hover:text-white">About Us</a>
                <a href="careers.html" class="nav-link text-gray-300 hover:text-white">Careers</a>
                <a href="mailto:<EMAIL>" class="mt-6 bg-gradient-to-r from-lime-500 to-green-600 hover:from-lime-600 hover:to-green-700 text-white px-6 py-3 rounded-lg transition duration-300 flex items-center space-x-2">
                  <i class="fas fa-envelope"></i>
                  <span>Contact</span>
                </a>
                <button onclick="toggleFullScreen()" class="mt-6 bg-lime-600 hover:bg-lime-700 text-white px-6 py-3 rounded-lg transition duration-300 flex items-center space-x-2">
                  <i class="fas fa-expand"></i>
                  <span>Fullscreen</span>
                </button>
    </div>
  </div>

  <!-- Main Content -->
  <main class="container mx-auto px-6 py-12 relative z-10">
  <!-- Header -->
  <header class="mb-16 text-center">
    <h1 class="heading-font text-5xl md:text-6xl gradient-text mb-4">About Us</h1>
    <p class="text-gray-400 text-xl">Innovation, Integrity, and Impact</p>
  </header>

  <!-- About Us Content Card -->
  <div class="max-w-4xl mx-auto glass-card content-card">
    <!-- Core Services -->
    <h2 class="heading-font text-3xl text-white mb-4">Core Services</h2>
    <p class="text-gray-400 text-lg mb-6">
      Xerv powers today’s digital experiences with world-class front-end and back-end development, UI/UX design, and image-editing services, setting the stage for tomorrow’s innovations.
    </p>

    <!-- NeTuArk -->
    <h2 class="heading-font text-3xl text-white mb-4">NeTuArk</h2>
    <p class="text-gray-400 text-lg mb-6">
      Our flagship social platform reimagines connection with personalized AI-powered chat, real-time collaboration, and advanced privacy controls—building more meaningful, engaging communities.
    </p>

    <!-- Wearable AI -->
    <h2 class="heading-font text-3xl text-white mb-4">Wearable AI</h2>
    <p class="text-gray-400 text-lg mb-6">
      From health-monitoring patches to gesture-controlled smart bands, we’re embedding intelligence in the things you wear—making technology a seamless extension of the human body.
    </p>

    <!-- Smart Vehicles -->
    <h2 class="heading-font text-3xl text-white mb-4">Smart Vehicles</h2>
    <p class="text-gray-400 text-lg mb-6">
      Zeta’s automotive line combines sleek design with autonomous driving and predictive safety systems, creating the next generation of self-driving cars.
    </p>

    <!-- Robotics for Everyone -->
    <h2 class="heading-font text-3xl text-white mb-4">Robotics for Everyone</h2>
    <p class="text-gray-400 text-lg mb-6">
      Beyond industrial and service robots, we’re pioneering assistive modular limbs for users with impairments—alongside a full suite of robotic solutions to augment human capability in homes, hospitals, and factories.
    </p>

    <!-- Space Exploration -->
    <h2 class="heading-font text-3xl text-white mb-4">Space Exploration</h2>
    <p class="text-gray-400 text-lg mb-6">
      Xerv Labs explores orbital and deep-space technologies—from satellite constellations to planetary probes—laying the groundwork for humanity’s next frontier.
    </p>

    <!-- Martian Subsidiaries -->
    <h2 class="heading-font text-3xl text-white mb-4">Martian Subsidiaries</h2>
    <ul class="list-disc list-inside text-gray-400 text-lg mb-6">
      <li><strong>Underground Transit:</strong> Automated tunneling systems and hyper-loop networks to connect future Martian habitats.</li>
      <li><strong>Operating System:</strong> Custom OS optimized for off-Earth networks, power constraints, and extreme environments.</li>
      <li><strong>Mobile Devices:</strong> Rugged, radiation-hardened smartphones and tablets designed for Martian settlers.</li>
    </ul>
  </div>
</main>


  <!-- Footer -->
  <footer class="mt-32 pb-12">
    <div class="max-w-4xl mx-auto glass-card p-8">
      <div class="flex flex-col md:flex-row justify-between items-center">
        <div class="mb-8 md:mb-0">
          <div class="flex items-center space-x-3 mb-4">
            <div class="w-10 h-10 bg-gradient-to-br from-lime-500 to-green-600 rounded-lg flex items-center justify-center">
              <span class="text-white text-sm font-bold">X</span>
            </div>
            <span class="heading-font text-xl gradient-text">Xerv</span>
          </div>
          <p class="text-gray-400">Innovation Without Compromise</p>
        </div>
        <div class="flex space-x-6">
          <a href="mailto:<EMAIL>" class="text-gray-400 hover:text-white transition">
            <i class="fas fa-envelope text-xl"></i>
          </a>
          <a href="https://www.linkedin.com/company/xerv-official/" target="_blank" class="text-gray-400 hover:text-white transition">
            <i class="fab fa-linkedin text-xl"></i>
          </a>
          <a href="https://twitter.com/XervTech" target="_blank" class="text-gray-400 hover:text-white transition">
            <i class="fab fa-twitter text-xl"></i>
          </a>
          <a href="https://www.instagram.com/xervtech/" target="_blank" class="text-gray-400 hover:text-white transition">
            <i class="fab fa-instagram text-xl"></i>
          </a>
          <a href="https://www.facebook.com/xervtech" target="_blank" class="text-gray-400 hover:text-white transition">
            <i class="fab fa-facebook text-xl"></i>
          </a>
        </div>
      </div>
      <div class="mt-8 pt-8 border-t border-gray-800 text-center text-gray-500">
        &copy; 2025 Xerv. All rights reserved.
      </div>
    </div>
  </footer>

  <script>
    // Three.js setup
    function initThreeJS() {
      const scene = new THREE.Scene();
      const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
      camera.position.z = 5;
      const renderer = new THREE.WebGLRenderer({ alpha: true });
      renderer.setSize(window.innerWidth, window.innerHeight);
      renderer.setClearColor(0x000000, 0);
      document.getElementById('three-canvas').appendChild(renderer.domElement);

      const nodes = [];
      const nodeCount = 50;
      const nodeGeometry = new THREE.SphereGeometry(0.05, 16, 16);
      for (let i = 0; i < nodeCount; i++) {
        const material = new THREE.MeshBasicMaterial({
          color: new THREE.Color(0.6 + 0.4 * Math.random(), 0.9 + 0.1 * Math.random(), 0.2 + 0.3 * Math.random())
        });
        const node = new THREE.Mesh(nodeGeometry, material);
        const radius = 4;
        const theta = Math.random() * Math.PI * 2;
        const phi = Math.random() * Math.PI;
        node.position.x = radius * Math.sin(phi) * Math.cos(theta);
        node.position.y = radius * Math.sin(phi) * Math.sin(theta);
        node.position.z = radius * Math.cos(phi);
        node.userData = {
          velocity: new THREE.Vector3((Math.random() - 0.5) * 0.01, (Math.random() - 0.5) * 0.01, (Math.random() - 0.5) * 0.01)
        };
        scene.add(node);
        nodes.push(node);
      }

      const connections = [];
      const lineMaterial = new THREE.LineBasicMaterial({ color: 0xA3E635, transparent: true, opacity: 0.4 });
      for (let i = 0; i < nodeCount; i++) {
        const connectCount = 2 + Math.floor(Math.random() * 2);
        for (let j = 0; j < connectCount; j++) {
          if (i !== j) {
            const lineGeometry = new THREE.BufferGeometry().setFromPoints([nodes[i].position, nodes[j].position]);
            const line = new THREE.Line(lineGeometry, lineMaterial);
            scene.add(line);
            connections.push({ line: line, from: i, to: j });
          }
        }
      }

      function animate() {
        requestAnimationFrame(animate);
        nodes.forEach(node => {
          node.position.add(node.userData.velocity);
          const distance = node.position.length();
          const maxRadius = 4;
          if (distance > maxRadius) {
            node.position.setLength(maxRadius);
            node.userData.velocity.reflect(node.position.clone().normalize());
          }
          node.rotation.x += 0.01;
          node.rotation.y += 0.01;
        });
        connections.forEach(connection => {
          const points = [nodes[connection.from].position.clone(), nodes[connection.to].position.clone()];
          connection.line.geometry.setFromPoints(points);
          connection.line.geometry.attributes.position.needsUpdate = true;
        });
        scene.rotation.y += 0.001;
        scene.rotation.x += 0.0005;
        renderer.render(scene, camera);
      }

      window.addEventListener('resize', () => {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
      });

      animate();
    }

    document.addEventListener('DOMContentLoaded', initThreeJS);

    // Mobile Menu Toggle
    function toggleMenu() {
      document.getElementById("mobileMenu").classList.toggle("open");
      document.getElementById("menuOverlay").classList.toggle("show");
    }

    // Fullscreen Toggle
    function toggleFullScreen() {
      if (!document.fullscreenElement && !document.mozFullScreenElement && !document.webkitFullscreenElement) {
        if (document.documentElement.requestFullscreen) {
          document.documentElement.requestFullscreen();
        } else if (document.documentElement.mozRequestFullScreen) {
          document.documentElement.mozRequestFullScreen();
        } else if (document.documentElement.webkitRequestFullscreen) {
          document.documentElement.webkitRequestFullscreen();
        } else if (document.documentElement.msRequestFullscreen) {
          document.documentElement.msRequestFullscreen();
        }
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
      }
    }

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function(e) {
        e.preventDefault();
        const targetId = this.getAttribute('href');
        const targetElement = document.querySelector(targetId);
        if (targetElement) {
          window.scrollTo({ top: targetElement.offsetTop, behavior: 'smooth' });
          document.getElementById("mobileMenu").classList.remove("open");
          document.getElementById("menuOverlay").classList.remove("show");
        }
      });
    });

    // Disable text selection, copy, and paste
    document.body.oncopy = document.body.onpaste = function() { return false; };
  </script>
</body>
</html>
