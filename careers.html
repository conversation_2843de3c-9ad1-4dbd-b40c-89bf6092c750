<!DOCTYPE html>
<html lang="en">
<head>
  <meta name="description" content="Zeta Technologies delivers next-gen AI solutions for social media and digital transformation. Discover NeTuArk and RoBERTo.">
<meta name="robots" content="index, follow">
<meta name="author" content="Zeta Technologies">
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Zeta Technologies - Careers</title>
  <!-- Modern Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=DM+Sans:wght@400;500;700&display=swap" rel="stylesheet">
  <!-- Tailwind CSS -->
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <!-- Three.js -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
  <style>
    :root {
      --primary: #6366f1;
      --primary-dark: #4f46e5;
      --secondary: #f59e0b;
      --dark: #111827;
      --light: #f5f3ff;
    }

    body {
      font-family: 'DM Sans', sans-serif;
      background-color: #080808;
      color: #ffffff;
      user-select: none;
      overflow-x: hidden;
    }

    .heading-font {
      font-family: 'Space Grotesk', sans-serif;
      font-weight: 600;
    }

    /* Glassmorphism card effect */
    .glass-card {
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 1rem;
      transition: all 0.4s ease;
    }

    .glass-card:hover {
      transform: translateY(-10px);
      background: rgba(255, 255, 255, 0.1);
      box-shadow: 0 10px 30px rgba(99, 102, 241, 0.2);
    }

    /* Gradient text */
    .gradient-text {
      background: linear-gradient(90deg, #6366f1, #8b5cf6, #d946ef);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    /* Button */
    .btn-primary {
      background: linear-gradient(135deg, #6366f1, #8b5cf6);
      padding: 0.75rem 1.5rem;
      border-radius: 8px;
      font-weight: 600;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .btn-primary:hover {
      transform: translateY(-3px);
      box-shadow: 0 10px 20px rgba(99, 102, 241, 0.4);
    }

    /* 3D canvas */
    #three-canvas {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100vh;
      z-index: -1;
    }

    /* Navigation Menu */
    .nav-link {
      position: relative;
      padding: 0.5rem 0;
      margin: 0 1rem;
      transition: all 0.3s ease;
      color: #ccc;
      text-decoration: none;
    }

    .nav-link:hover {
      color: #fff;
    }

    .nav-link::after {
      content: '';
      position: absolute;
      width: 0;
      height: 2px;
      bottom: 0;
      left: 0;
      background-color: var(--primary);
      transition: width 0.3s ease;
    }

    .nav-link:hover::after {
      width: 100%;
    }

    /* Mobile menu */
    .mobile-menu {
      position: fixed;
      top: 0;
      right: -100%;
      width: 80%;
      height: 100%;
      background: rgba(0, 0, 0, 0.95);
      backdrop-filter: blur(10px);
      transition: right 0.4s cubic-bezier(0.77, 0, 0.175, 1);
      z-index: 20;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }

    .mobile-menu.open {
      right: 0;
    }

    /* Overlay for mobile menu */
    .menu-overlay {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 15;
    }

    .menu-overlay.show {
      display: block;
    }

    /* Service number badge */
    .number-badge {
      background: linear-gradient(135deg, #f59e0b 0%, #fb923c 100%);
      box-shadow: 0 4px 10px rgba(245, 158, 11, 0.3);
    }
  </style>
</head>
<body class="min-h-screen" oncontextmenu="return false" oncopy="return false" onpaste="return false">
  <!-- Three.js Canvas -->
  <div id="three-canvas"></div>

  <!-- Navigation Menu -->
  <nav class="container mx-auto px-6 py-6 relative z-10">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-3">
        <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
          <img src="Screenshot_20250104-002946-removebg-preview.png" alt="Zeta Technologies AI Platform Overview">
        </div>
        <span class="heading-font text-2xl gradient-text">Zeta Technologies</span>
      </div>
      <!-- Desktop Navigation -->
      <div class="hidden md:flex items-center space-x-4">
        <a href="index.html" class="nav-link">Home</a>
        <a href="products.html" class="nav-link">Products</a>
        <a href="services.html" class="nav-link">Services</a>
        <a href="about.html" class="nav-link">About Us</a>
        <a href="careers.html" class="nav-link text-white font-bold">Careers</a>
        <a href="https://ko-fi.com/ZetaTech" target="_blank" class="ml-2 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-4 py-2 rounded-lg transition duration-300 flex items-center space-x-2">
          <i class="fas fa-mug-hot"></i>
          <span class="hidden lg:inline">Support Us</span>
        </a>
      </div>
      <!-- Hamburger Menu for Mobile -->
      <div class="md:hidden flex items-center">
        <div class="hamburger text-2xl text-white" onclick="toggleMenu()">
          <i class="fas fa-bars"></i>
        </div>
      </div>
    </div>
  </nav>

  <!-- Mobile Menu -->
  <div id="menuOverlay" class="menu-overlay" onclick="toggleMenu()"></div>
  <div id="mobileMenu" class="mobile-menu">
    <div class="absolute top-6 right-6 text-2xl text-white" onclick="toggleMenu()">
      <i class="fas fa-times"></i>
    </div>
    <div class="flex flex-col items-center space-y-8">
      <a href="index.html" class="nav-link">Home</a>
      <a href="products.html" class="nav-link">Products</a>
      <a href="services.html" class="nav-link">Services</a>
      <a href="about.html" class="nav-link">About Us</a>
      <a href="careers.html" class="nav-link text-white font-bold">Careers</a>
      <a href="https://ko-fi.com/ZetaTech" target="_blank" class="mt-6 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-6 py-3 rounded-lg transition duration-300 flex items-center space-x-2">
        <i class="fas fa-mug-hot"></i>
        <span>Support Us</span>
      </a>
    </div>
  </div>

  <!-- Main Content -->
  <main class="container mx-auto px-6 py-12 relative z-10">
    <header class="mb-16 text-center">
      <h1 class="heading-font text-5xl md:text-6xl gradient-text mb-4">Internship Opportunities</h1>
      <p class="text-gray-400 text-xl">Launch your career with hands-on experience at Zeta Technologies.</p>
    </header>

    <!-- Internships Section -->
    <section class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">

      <!-- Internship 1 -->
<!-- Internship 1: Front End Developer -->
<div class="glass-card p-8">
  <h3 class="heading-font text-2xl font-bold text-white mb-2">Front End Developer Intern</h3>
  <p class="text-gray-400 text-lg mb-4">Work with modern JavaScript frameworks to build stunning UIs.</p>
  <a
    href="mailto:<EMAIL>?subject=Application%20for%20Front-End%20Developer%20Internship&body=Hi%20Zeta%20Team%2C%0A%0AI%20am%20interested%20in%20applying%20for%20the%20Front-End%20Developer%20Internship.%20Please%20find%20my%20resume%20attached.%0A%0AThank%20you!"
    class="btn-primary block text-center text-white">
    Apply Now
  </a>
</div>

<!-- Internship 2: Back End Developer -->
<div class="glass-card p-8">
  <h3 class="heading-font text-2xl font-bold text-white mb-2">Back End Developer Intern</h3>
  <p class="text-gray-400 text-lg mb-4">Gain experience in databases, APIs, and cloud computing.</p>
  <a
    href="mailto:<EMAIL>?subject=Application%20for%20Back-End%20Developer%20Internship&body=Hi%20Zeta%20Team%2C%0A%0AI%20am%20excited%20to%20apply%20for%20the%20Back-End%20Developer%20Internship%20role.%20Please%20find%20my%20details%20attached.%0A%0ARegards!"
    class="btn-primary block text-center text-white">
    Apply Now
  </a>
</div>

<!-- Internship 3: Full-Stack Developer -->
<div class="glass-card p-8">
  <h3 class="heading-font text-2xl font-bold text-white mb-2">Full-Stack Developer Intern</h3>
  <p class="text-gray-400 text-lg mb-4">Build and maintain full web applications using both front-end and back-end technologies.</p>
  <a
    href="mailto:<EMAIL>?subject=Application%20for%20Full-Stack%20Developer%20Internship&body=Hi%20Zeta%20Team%2C%0A%0AI%20am%20excited%20to%20apply%20for%20the%20Full-Stack%20Developer%20Internship.%20I%20have%20experience%20with%20both%20front-end%20and%20back-end%20technologies.%20Please%20find%20my%20resume%20attached.%0A%0AThanks%20and%20regards!"
    class="btn-primary block text-center text-white">
    Apply Now
  </a>
</div>

      <div class="glass-card p-8">
  <h3 class="heading-font text-2xl font-bold text-white mb-2">Salesperson Intern</h3>
  <p class="text-gray-400 text-lg mb-4">Join our dynamic team to help drive growth through lead generation, client engagement, and strategic sales initiatives.</p>
  <a
    href="mailto:<EMAIL>?subject=Application%20for%20Salesperson%20Internship&body=Hi%20Zeta%20Team%2C%0A%0AI%20am%20interested%20in%20applying%20for%20the%20Salesperson%20Internship%20position.%20I%20am%20eager%20to%20contribute%20to%20your%20team's%20growth%20and%20develop%20my%20skills%20in%20sales%20and%20client%20relations.%20Please%20find%20my%20resume%20attached.%0A%0AThanks%20and%20regards!"
    class="btn-primary block text-center text-white">
    Apply Now
  </a>
      </div>

<!-- Internship 4: UI/UX Designer -->
<div class="glass-card p-8">
  <h3 class="heading-font text-2xl font-bold text-white mb-2">UI/UX Designer Intern</h3>
  <p class="text-gray-400 text-lg mb-4">Design user-friendly interfaces with industry tools.</p>
  <a
    href="mailto:<EMAIL>?subject=Application%20for%20UI%2FUX%20Designer%20Internship&body=Hello%20Zeta%20Team%2C%0A%0AI%20would%20like%20to%20apply%20for%20the%20UI%2FUX%20Designer%20Intern%20position.%20Please%20review%20my%20portfolio%20and%20resume.%0A%0AThank%20you!"
    class="btn-primary block text-center text-white">
    Apply Now
  </a>
</div>

    </section>

    <!-- Footer -->
    <footer class="mt-32 pb-12">
      <div class="max-w-4xl mx-auto glass-card p-8">
        <div class="flex flex-col md:flex-row justify-between items-center">
          <div class="mb-8 md:mb-0">
            <div class="flex items-center space-x-3 mb-4">
              <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                <img src="Screenshot_20250104-002946-removebg-preview.png" alt="Zeta Technologies AI Platform Overview">
              </div>
              <span class="heading-font text-xl gradient-text">Zeta Technologies</span>
            </div>
            <p class="text-gray-400">Innovation Without Compromise</p>
          </div>
          <div class="flex space-x-6">
            <a href="https://x.com/TechAtZeta?t=f5UsigfHtK2gUjF5zQhpPQ&s=09" target="_blank" class="text-gray-400 hover:text-white transition">
              <i class="fab fa-twitter text-xl"></i>
            </a>
            <a href="https://web.facebook.com/profile.php?id=61576226673602" target="_blank" class="text-gray-400 hover:text-white transition">
              <i class="fab fa-facebook text-xl"></i>
            </a>
            <a href="https://www.linkedin.com/company/phase-official/" target="_blank" class="text-gray-400 hover:text-white transition">
              <i class="fab fa-linkedin text-xl"></i>
            </a>
            <a href="https://www.instagram.com/zetatechhq/" target="_blank" class="text-gray-400 hover:text-white transition">
              <i class="fab fa-instagram text-xl"></i>
            </a>
            <a href="https://ko-fi.com/ZetaTech" target="_blank" class="text-blue-400 hover:text-blue-300 transition">
              <i class="fas fa-mug-hot text-xl"></i>
            </a>
          </div>
        </div>
        <div class="mt-8 pt-8 border-t border-gray-800 text-center text-gray-500">
          &copy; 2025 Zeta Technologies. All rights reserved.
        </div>
      </div>
    </footer>
  </main>

  <!-- Three.js Setup -->
  <script>
    function initThreeJS() {
      const scene = new THREE.Scene();
      const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
      camera.position.z = 5;
      const renderer = new THREE.WebGLRenderer({ alpha: true });
      renderer.setSize(window.innerWidth, window.innerHeight);
      renderer.setClearColor(0x000000, 0);
      document.getElementById('three-canvas').appendChild(renderer.domElement);

      // Create network nodes
      const nodes = [];
      const nodeCount = 50;
      const nodeGeometry = new THREE.SphereGeometry(0.05, 16, 16);

      for (let i = 0; i < nodeCount; i++) {
        const material = new THREE.MeshBasicMaterial({
          color: new THREE.Color(0.5 + 0.5 * Math.random(), 0.5 + 0.5 * Math.random(), 0.9 + 0.1 * Math.random())
        });
        const node = new THREE.Mesh(nodeGeometry, material);
        const radius = 4;
        const theta = Math.random() * Math.PI * 2;
        const phi = Math.random() * Math.PI;
        node.position.x = radius * Math.sin(phi) * Math.cos(theta);
        node.position.y = radius * Math.sin(phi) * Math.sin(theta);
        node.position.z = radius * Math.cos(phi);
        node.userData = {
          velocity: new THREE.Vector3((Math.random() - 0.5) * 0.01, (Math.random() - 0.5) * 0.01, (Math.random() - 0.5) * 0.01)
        };
        scene.add(node);
        nodes.push(node);
      }

      // Create connections between nodes
      const connections = [];
      const lineMaterial = new THREE.LineBasicMaterial({
        color: 0x6366f1,
        transparent: true,
        opacity: 0.4
      });
      for (let i = 0; i < nodeCount; i++) {
        const connectCount = 2 + Math.floor(Math.random() * 2);
        for (let j = 0; j < connectCount; j++) {
          if (i !== j) {
            const lineGeometry = new THREE.BufferGeometry().setFromPoints([nodes[i].position, nodes[j].position]);
            const line = new THREE.Line(lineGeometry, lineMaterial);
            scene.add(line);
            connections.push({ line: line, from: i, to: j });
          }
        }
      }

      function animate() {
        requestAnimationFrame(animate);
        nodes.forEach(node => {
          node.position.add(node.userData.velocity);
          const distance = node.position.length();
          const maxRadius = 4;
          if (distance > maxRadius) {
            node.position.setLength(maxRadius);
            node.userData.velocity.reflect(node.position.clone().normalize());
          }
          node.rotation.x += 0.01;
          node.rotation.y += 0.01;
        });
        connections.forEach(connection => {
          const points = [nodes[connection.from].position.clone(), nodes[connection.to].position.clone()];
          connection.line.geometry.setFromPoints(points);
          connection.line.geometry.attributes.position.needsUpdate = true;
        });
        scene.rotation.y += 0.001;
        scene.rotation.x += 0.0005;
        renderer.render(scene, camera);
      }

      window.addEventListener('resize', () => {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
      });

      animate();
    }

    document.addEventListener('DOMContentLoaded', initThreeJS);

    // Mobile Menu Toggle
    function toggleMenu() {
      document.getElementById("mobileMenu").classList.toggle("open");
      document.getElementById("menuOverlay").classList.toggle("show");
    }

    // Fullscreen Toggle
    function toggleFullScreen() {
      if (!document.fullscreenElement && !document.mozFullScreenElement && !document.webkitFullscreenElement) {
        if (document.documentElement.requestFullscreen) {
          document.documentElement.requestFullscreen();
        } else if (document.documentElement.mozRequestFullScreen) {
          document.documentElement.mozRequestFullScreen();
        } else if (document.documentElement.webkitRequestFullscreen) {
          document.documentElement.webkitRequestFullscreen();
        } else if (document.documentElement.msRequestFullscreen) {
          document.documentElement.msRequestFullscreen();
        }
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
      }
    }

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function(e) {
        e.preventDefault();
        const targetId = this.getAttribute('href');
        const targetElement = document.querySelector(targetId);
        if (targetElement) {
          window.scrollTo({ top: targetElement.offsetTop, behavior: 'smooth' });
          document.getElementById("mobileMenu").classList.remove("open");
          document.getElementById("menuOverlay").classList.remove("show");
        }
      });
    });

    // Disable text selection, copy, and paste
    document.body.oncopy = document.body.onpaste = function() { return false; };
  </script>
</body>
    </html>
