<!DOCTYPE html>
<html lang="en">
<head>
  <meta name="description" content="Explore Zeta Technologies' portfolio of work and successful projects. See our team's achievements in UI/UX, development, and design.">
  <meta name="robots" content="index, follow">
  <meta name="author" content="Zeta Technologies">
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Zeta Technologies - Our Work</title>
  <!-- Modern fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=DM+Sans:wght@400;500;700&display=swap" rel="stylesheet">
  <!-- Tailwind CSS -->
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <!-- Three.js -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
  <style>
    :root {
      --primary: #6366f1;
      --primary-dark: #4f46e5;
      --secondary: #f59e0b;
      --dark: #111827;
      --light: #f5f3ff;
    }

    body {
      font-family: 'DM Sans', sans-serif;
      background-color: #080808;
      color: #ffffff;
      user-select: none;
      overflow-x: hidden;
    }

    .heading-font {
      font-family: 'Space Grotesk', sans-serif;
      font-weight: 600;
    }

    /* Glassmorphism card effect */
    .glass-card {
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 1rem;
      transition: all 0.4s ease;
    }

    .glass-card:hover {
      transform: translateY(-10px);
      background: rgba(255, 255, 255, 0.1);
      box-shadow: 0 10px 30px rgba(99, 102, 241, 0.2);
    }

    /* Gradient text */
    .gradient-text {
      background: linear-gradient(90deg, #6366f1, #8b5cf6, #d946ef);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-size: 300% 100%;
      animation: gradient-shift 8s ease infinite;
    }

    @keyframes gradient-shift {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }

    /* 3D canvas */
    #three-canvas {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100vh;
      z-index: -1;
    }

    /* Navigation Menu */
    .nav-link {
      position: relative;
      padding: 0.5rem 0;
      margin: 0 1rem;
      transition: all 0.3s ease;
    }

    .nav-link::after {
      content: '';
      position: absolute;
      width: 0;
      height: 2px;
      bottom: 0;
      left: 0;
      background-color: var(--primary);
      transition: width 0.3s ease;
    }

    .nav-link:hover::after {
      width: 100%;
    }

    /* Mobile menu */
    .mobile-menu {
      position: fixed;
      top: 0;
      right: -100%;
      width: 80%;
      height: 100%;
      background: rgba(0, 0, 0, 0.95);
      backdrop-filter: blur(10px);
      transition: right 0.4s cubic-bezier(0.77, 0, 0.175, 1);
      z-index: 20;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }

    .mobile-menu.open {
      right: 0;
    }

    /* Overlay for mobile menu */
    .menu-overlay {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 15;
    }

    .menu-overlay.show {
      display: block;
    }
  </style>
</head>
<body class="min-h-screen" oncontextmenu="return false" oncopy="return false" onpaste="return false">
  <!-- Three.js Canvas -->
  <div id="three-canvas"></div>

  <!-- Navigation Menu -->
  <nav class="container mx-auto px-6 py-6 relative z-10">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-3">
        <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
          <img src="Screenshot_20250104-002946-removebg-preview.png" alt="Zeta Technologies AI Platform Overview">
        </div>
        <span class="heading-font text-2xl gradient-text">Zeta Technologies</span>
      </div>

      <!-- Hamburger Menu for Mobile -->
      <div class="md:hidden flex items-center">
        <div class="hamburger text-2xl text-white" onclick="toggleMenu()">
          <i class="fas fa-bars"></i>
        </div>
      </div>

      <!-- Desktop Navigation (visible on medium+ screens) -->
      <div class="hidden md:flex items-center space-x-4">
        <a href="index.html" class="nav-link text-gray-300 hover:text-white">Home</a>
        <a href="products.html" class="nav-link text-gray-300 hover:text-white">Products</a>
        <a href="services.html" class="nav-link text-gray-300 hover:text-white">Services</a>
        <a href="work.html" class="nav-link text-gray-300 hover:text-white">Our Work</a>
        <a href="about.html" class="nav-link text-gray-300 hover:text-white">About Us</a>
        <a href="careers.html" class="nav-link text-gray-300 hover:text-white">Careers</a>
        <a href="https://ko-fi.com/ZetaTech" target="_blank" class="ml-2 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-4 py-2 rounded-lg transition duration-300 flex items-center space-x-2">
          <i class="fas fa-mug-hot"></i>
          <span class="hidden lg:inline">Support Us</span>
        </a>
        <button onclick="toggleFullScreen()" class="ml-4 bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition duration-300 flex items-center space-x-2">
          <i class="fas fa-expand"></i>
          <span class="hidden lg:inline">Fullscreen</span>
        </button>
      </div>
    </div>
  </nav>

  <!-- Mobile Menu -->
  <div id="menuOverlay" class="menu-overlay" onclick="toggleMenu()"></div>
  <div id="mobileMenu" class="mobile-menu">
    <div class="absolute top-6 right-6 text-2xl text-white" onclick="toggleMenu()">
      <i class="fas fa-times"></i>
    </div>
    <div class="flex flex-col items-center space-y-8">
      <a href="index.html" class="nav-link text-gray-300 hover:text-white">Home</a>
      <a href="products.html" class="nav-link text-gray-300 hover:text-white">Products</a>
      <a href="services.html" class="nav-link text-gray-300 hover:text-white">Services</a>
      <a href="work.html" class="nav-link text-gray-300 hover:text-white">Our Work</a>
      <a href="about.html" class="nav-link text-gray-300 hover:text-white">About Us</a>
      <a href="careers.html" class="nav-link text-gray-300 hover:text-white">Careers</a>
      <a href="https://ko-fi.com/ZetaTech" target="_blank" class="mt-6 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-6 py-3 rounded-lg transition duration-300 flex items-center space-x-2">
        <i class="fas fa-mug-hot"></i>
        <span>Support Us</span>
      </a>
      <button onclick="toggleFullScreen()" class="mt-6 bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg transition duration-300 flex items-center space-x-2">
        <i class="fas fa-expand"></i>
        <span>Fullscreen</span>
      </button>
    </div>
  </div>

  <!-- Main Content -->
  <main class="container mx-auto px-6 py-12 relative z-10">
    <header class="mb-16 text-center">
      <h1 class="heading-font text-5xl md:text-6xl gradient-text mb-4">Our Work</h1>
      <p class="text-gray-400 text-xl">Explore our latest projects and achievements</p>
    </header>

    <!-- Work Portfolio Section -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
      <!-- Card 1: WADDLE App -->
      <a href="https://embed.figma.com/design/Lqwgk5O6IaImwfTsedGOAy/WADDLE-APP?node-id=2-101657&embed-host=share"
         target="_blank" rel="noopener noreferrer" class="block">
        <div class="glass-card p-6 hover:scale-105 transition-all duration-300">
          <div class="text-center mb-4">
            <span class="inline-block p-3 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 text-white text-3xl">
              <i class="fas fa-figma"></i>
            </span>
          </div>
          <h3 class="heading-font text-xl font-bold text-white mb-2 text-center">WADDLE App Prototype</h3>
          <p class="text-gray-400">Interactive UI/UX prototype created in Figma with navigation flows and design system.</p>
          <div class="mt-4 flex justify-center space-x-2">
            <span class="px-2 py-1 bg-red-900 bg-opacity-30 rounded-md text-red-400 text-xs">Figma</span>
          </div>
          <p class="text-gray-300 italic text-center mt-4">By Johnpaul</p>
        </div>
      </a>

      <!-- Card 2: Paypi App -->
      <a href="https://www.figma.com/design/s7Zz9FMzAdoQi9OC6Uj5Md/Paypi-App?node-id=0-1&t=mh86NtCJmzuBSW2y-1"
         target="_blank" rel="noopener noreferrer" class="block">
        <div class="glass-card p-6 hover:scale-105 transition-all duration-300">
          <div class="text-center mb-4">
            <span class="inline-block p-3 rounded-full bg-gradient-to-br from-pink-500 to-red-600 text-white text-3xl">
              <i class="fas fa-figma"></i>
            </span>
          </div>
          <h3 class="heading-font text-xl font-bold text-white mb-2 text-center">Paypi App Prototype</h3>
          <p class="text-gray-400">High-fidelity payment app design with user flows, components, and interactive previews.</p>
          <div class="mt-4 flex justify-center space-x-2">
            <span class="px-2 py-1 bg-red-900 bg-opacity-30 rounded-md text-red-400 text-xs">Figma</span>
          </div>
          <p class="text-gray-300 italic text-center mt-4">By Johnpaul</p>
        </div>
      </a>

      <!-- Card 3: Milanochi Shopping Cart -->
      <a href="https://shopping-cart-milanochi.vercel.app"
         target="_blank" rel="noopener noreferrer" class="block">
        <div class="glass-card p-6 hover:scale-105 transition-all duration-300">
          <div class="text-center mb-4">
            <span class="inline-block p-3 rounded-full bg-gradient-to-br from-blue-500 to-teal-600 text-white text-3xl">
              <i class="fas fa-shopping-cart"></i>
            </span>
          </div>
          <h3 class="heading-font text-xl font-bold text-white mb-2 text-center">Milanochi Shopping Cart</h3>
          <p class="text-gray-400">React-based e-commerce front end with product listing, cart functionality, and checkout flow.</p>
          <div class="mt-4 flex justify-center space-x-2">
            <span class="px-2 py-1 bg-blue-900 bg-opacity-30 rounded-md text-blue-400 text-xs">React</span>
            <span class="px-2 py-1 bg-teal-900 bg-opacity-30 rounded-md text-teal-400 text-xs">Tailwind CSS</span>
          </div>
          <p class="text-gray-300 italic text-center mt-4">By Daniel</p>
        </div>
      </a>

      <!-- Card 4: Lendsqr FE Test -->
      <a href="https://ochi-daniel-lendsqr-fe-test.vercel.app"
         target="_blank" rel="noopener noreferrer" class="block">
        <div class="glass-card p-6 hover:scale-105 transition-all duration-300">
          <div class="text-center mb-4">
            <span class="inline-block p-3 rounded-full bg-gradient-to-br from-blue-600 to-indigo-700 text-white text-3xl">
              <i class="fas fa-laptop-code"></i>
            </span>
          </div>
          <h3 class="heading-font text-xl font-bold text-white mb-2 text-center">Lendsqr Frontend Test</h3>
          <p class="text-gray-400">Assessment project covering user dashboard, data tables, and responsive layouts.</p>
          <div class="mt-4 flex justify-center space-x-2">
            <span class="px-2 py-1 bg-blue-900 bg-opacity-30 rounded-md text-blue-400 text-xs">React</span>
            <span class="px-2 py-1 bg-gray-900 bg-opacity-30 rounded-md text-gray-400 text-xs">Ant Design</span>
          </div>
          <p class="text-gray-300 italic text-center mt-4">By Daniel</p>
        </div>
      </a>

      <!-- Card 5: Construction Landing Page -->
      <a href="https://construction-landing-page-blush.vercel.app/"
         target="_blank" rel="noopener noreferrer" class="block">
        <div class="glass-card p-6 hover:scale-105 transition-all duration-300">
          <div class="text-center mb-4">
            <span class="inline-block p-3 rounded-full bg-gradient-to-br from-green-500 to-yellow-600 text-white text-3xl">
              <i class="fas fa-hard-hat"></i>
            </span>
          </div>
          <h3 class="heading-font text-xl font-bold text-white mb-2 text-center">Construction Landing Page</h3>
          <p class="text-gray-400">SEO-optimized marketing site with service sections, image gallery, and contact form.</p>
          <div class="mt-4 flex justify-center space-x-2">
            <span class="px-2 py-1 bg-gray-900 bg-opacity-30 rounded-md text-gray-400 text-xs">HTML</span>
            <span class="px-2 py-1 bg-blue-900 bg-opacity-30 rounded-md text-blue-400 text-xs">Tailwind CSS</span>
          </div>
          <p class="text-gray-300 italic text-center mt-4">By Abang</p>
        </div>
      </a>

      <!-- Card 6: SoundMap Africa -->
      <a href="https://www.soundmap.africa"
         target="_blank" rel="noopener noreferrer" class="block">
        <div class="glass-card p-6 hover:scale-105 transition-all duration-300">
          <div class="text-center mb-4">
            <span class="inline-block p-3 rounded-full bg-gradient-to-br from-orange-500 to-red-600 text-white text-3xl">
              <i class="fas fa-map-marked-alt"></i>
            </span>
          </div>
          <h3 class="heading-font text-xl font-bold text-white mb-2 text-center">SoundMap Africa</h3>
          <p class="text-gray-400">Music discovery platform integrating Mapbox for geotagged track exploration.</p>
          <div class="mt-4 flex justify-center space-x-2">
            <span class="px-2 py-1 bg-green-900 bg-opacity-30 rounded-md text-green-400 text-xs">MongoDB</span>
            <span class="px-2 py-1 bg-violet-900 bg-opacity-30 rounded-md text-violet-400 text-xs">Express</span>
            <span class="px-2 py-1 bg-cyan-900 bg-opacity-30 rounded-md text-cyan-400 text-xs">React</span>
            <span class="px-2 py-1 bg-green-900 bg-opacity-30 rounded-md text-green-400 text-xs">Node.js</span>
          </div>
          <p class="text-gray-300 italic text-center mt-4">By Ifeoluwa</p>
        </div>
      </a>

      <!-- Card 7: Flash Reviews -->
      <a href="https://www.flashreviews.ca"
         target="_blank" rel="noopener noreferrer" class="block">
        <div class="glass-card p-6 hover:scale-105 transition-all duration-300">
          <div class="text-center mb-4">
            <span class="inline-block p-3 rounded-full bg-gradient-to-br from-yellow-500 to-orange-600 text-white text-3xl">
              <i class="fas fa-star"></i>
            </span>
          </div>
          <h3 class="heading-font text-xl font-bold text-white mb-2 text-center">Flash Reviews</h3>
          <p class="text-gray-400">Quick feedback aggregator with rating system and user comments integration.</p>
          <div class="mt-4 flex justify-center space-x-2">
            <span class="px-2 py-1 bg-green-900 bg-opacity-30 rounded-md text-green-400 text-xs">MongoDB</span>
            <span class="px-2 py-1 bg-violet-900 bg-opacity-30 rounded-md text-violet-400 text-xs">Express</span>
            <span class="px-2 py-1 bg-cyan-900 bg-opacity-30 rounded-md text-cyan-400 text-xs">React</span>
            <span class="px-2 py-1 bg-green-900 bg-opacity-30 rounded-md text-green-400 text-xs">Node.js</span>
          </div>
          <p class="text-gray-300 italic text-center mt-4">By Ifeoluwa</p>
        </div>
      </a>

      <!-- Card 8: Quickero Platform -->
      <a href="https://quickeroplatform.vercel.app/"
         target="_blank" rel="noopener noreferrer" class="block">
        <div class="glass-card p-6 hover:scale-105 transition-all duration-300">
          <div class="text-center mb-4">
            <span class="inline-block p-3 rounded-full bg-gradient-to-br from-purple-500 to-blue-600 text-white text-3xl">
              <i class="fas fa-rocket"></i>
            </span>
          </div>
          <h3 class="heading-font text-xl font-bold text-white mb-2 text-center">Quickero Platform</h3>
          <p class="text-gray-400">Next.js app with task management features, real-time updates, and responsive design.</p>
          <div class="mt-4 flex justify-center space-x-2">
            <span class="px-2 py-1 bg-gray-900 bg-opacity-30 rounded-md text-gray-400 text-xs">Next.js</span>
            <span class="px-2 py-1 bg-blue-900 bg-opacity-30 rounded-md text-blue-400 text-xs">Tailwind CSS</span>
          </div>
          <p class="text-gray-300 italic text-center mt-4">By Abang</p>
        </div>
      </a>

      <!-- Card 9: JC Connects -->
      <a href="https://jcconnects.com"
         target="_blank" rel="noopener noreferrer" class="block">
        <div class="glass-card p-6 hover:scale-105 transition-all duration-300">
          <div class="text-center mb-4">
            <span class="inline-block p-3 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 text-white text-3xl">
              <i class="fas fa-users"></i>
            </span>
          </div>
          <h3 class="heading-font text-xl font-bold text-white mb-2 text-center">JC Connects</h3>
          <p class="text-gray-400">JC CONNENT is a full-featured e-commerce platform offering cart, search, payments, user/product management, admin tools, messaging, and loyalty points.</p>
          <div class="mt-4 flex justify-center space-x-2">
            <span class="px-2 py-1 bg-blue-900 bg-opacity-30 rounded-md text-blue-400 text-xs">React</span>
            <span class="px-2 py-1 bg-yellow-900 bg-opacity-30 rounded-md text-yellow-400 text-xs">Laravel</span>
            <span class="px-2 py-1 bg-violet-900 bg-opacity-30 rounded-md text-violet-400 text-xs">PHP</span>
          </div>
          <p class="text-gray-300 italic text-center mt-4">By Jonadab</p>
        </div>
      </a>

      <!-- Card 10: AI Inspin Showcase -->
      <a href="https://joweb1.github.io/Ai-Inspin/"
         target="_blank" rel="noopener noreferrer" class="block">
        <div class="glass-card p-6 hover:scale-105 transition-all duration-300">
          <div class="text-center mb-4">
            <span class="inline-block p-3 rounded-full bg-gradient-to-br from-purple-500 to-pink-600 text-white text-3xl">
              <i class="fas fa-brain"></i>
            </span>
          </div>
          <h3 class="heading-font text-xl font-bold text-white mb-2 text-center">AI Inspin Showcase</h3>
          <p class="text-gray-400">Static portfolio site highlighting AI-based design concepts and experiments.</p>
          <div class="mt-4 flex justify-center space-x-2">
            <span class="px-2 py-1 bg-gray-900 bg-opacity-30 rounded-md text-gray-400 text-xs">HTML</span>
            <span class="px-2 py-1 bg-gray-900 bg-opacity-30 rounded-md text-gray-400 text-xs">CSS</span>
            <span class="px-2 py-1 bg-gray-900 bg-opacity-30 rounded-md text-gray-400 text-xs">JavaScript</span>
          </div>
          <p class="text-gray-300 italic text-center mt-4">By Jonadab</p>
        </div>
      </a>

      <!-- Card 11: Refit Framer Template -->
      <a href="https://refit.framer.website/?via=jjgerrish"
         target="_blank" rel="noopener noreferrer" class="block">
        <div class="glass-card p-6 hover:scale-105 transition-all duration-300">
          <div class="text-center mb-4">
            <span class="inline-block p-3 rounded-full bg-gradient-to-br from-green-500 to-teal-600 text-white text-3xl">
              <i class="fas fa-home"></i>
            </span>
          </div>
          <h3 class="heading-font text-xl font-bold text-white mb-2 text-center">Refit Framer Template</h3>
          <p class="text-gray-400">Customizable Framer template tailored for home improvement and service businesses.</p>
          <div class="mt-4 flex justify-center space-x-2">
            <span class="px-2 py-1 bg-purple-900 bg-opacity-30 rounded-md text-purple-400 text-xs">Framer</span>
          </div>
          <p class="text-gray-300 italic text-center mt-4">By Osim</p>
        </div>
      </a>

      <!-- Card 12: Sonic Framer Template -->
      <a href="https://sonic.framer.media/"
         target="_blank" rel="noopener noreferrer" class="block">
        <div class="glass-card p-6 hover:scale-105 transition-all duration-300">
          <div class="text-center mb-4">
            <span class="inline-block p-3 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 text-white text-3xl">
              <i class="fas fa-music"></i>
            </span>
          </div>
          <h3 class="heading-font text-xl font-bold text-white mb-2 text-center">Sonic Framer Template</h3>
          <p class="text-gray-400">Dynamic Framer template for showcasing products, e-commerce, and portfolios.</p>
          <div class="mt-4 flex justify-center space-x-2">
            <span class="px-2 py-1 bg-purple-900 bg-opacity-30 rounded-md text-purple-400 text-xs">Framer</span>
          </div>
          <p class="text-gray-300 italic text-center mt-4">By Osim</p>
        </div>
      </a>
    </div>

    <!-- Contact Button Section -->
    <section class="mt-16 text-center">
      <a href="mailto:<EMAIL>" class="bg-indigo-600 hover:bg-indigo-700 text-white px-8 py-3 rounded-full transition duration-300 inline-flex items-center space-x-2">
        <i class="fas fa-envelope"></i>
        <span>Contact Us</span>
      </a>
      <a href="https://ko-fi.com/ZetaTech" target="_blank" class="ml-4 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-3 rounded-full transition duration-300 inline-flex items-center space-x-2">
        <i class="fas fa-mug-hot"></i>
        <span>Support Our Work</span>
      </a>
    </section>

    <!-- Footer -->
    <footer class="mt-32 pb-12">
      <div class="max-w-4xl mx-auto glass-card p-8">
        <div class="flex flex-col md:flex-row justify-between items-center">
          <div class="mb-8 md:mb-0">
            <div class="flex items-center space-x-3 mb-4">
              <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                <img src="Screenshot_20250104-002946-removebg-preview.png" alt="Zeta Technologies AI Platform Overview">
              </div>
              <span class="heading-font text-xl gradient-text">Zeta Technologies</span>
            </div>
            <p class="text-gray-400">Innovation Without Compromise</p>
          </div>
          <div class="flex space-x-6">
            <a href="https://x.com/TechAtZeta?t=f5UsigfHtK2gUjF5zQhpPQ&s=09" target="_blank" class="text-gray-400 hover:text-white transition">
              <i class="fab fa-twitter text-xl"></i>
            </a>
            <a href="https://web.facebook.com/profile.php?id=61576226673602" target="_blank" class="text-gray-400 hover:text-white transition">
              <i class="fab fa-facebook text-xl"></i>
            </a>
            <a href="https://www.linkedin.com/company/phase-official/" target="_blank" class="text-gray-400 hover:text-white transition">
              <i class="fab fa-linkedin text-xl"></i>
            </a>
            <a href="https://www.instagram.com/zetatechhq/" target="_blank" class="text-gray-400 hover:text-white transition">
              <i class="fab fa-instagram text-xl"></i>
            </a>
            <a href="https://ko-fi.com/ZetaTech" target="_blank" class="text-blue-400 hover:text-blue-300 transition">
              <i class="fas fa-mug-hot text-xl"></i>
            </a>
          </div>
        </div>
        <div class="mt-8 pt-8 border-t border-gray-800 text-center text-gray-500">
          &copy; 2025 Zeta Technologies. All rights reserved.
        </div>
      </div>
    </footer>
  </main>

  <!-- JavaScript for menu toggle and fullscreen -->
  <script>
    function toggleMenu() {
      const menu = document.getElementById('mobileMenu');
      const overlay = document.getElementById('menuOverlay');
      menu.classList.toggle('open');
      overlay.classList.toggle('show');
    }

    function toggleFullScreen() {
      if (!document.fullscreenElement && !document.mozFullScreenElement && !document.webkitFullscreenElement) {
        if (document.documentElement.requestFullscreen) {
          document.documentElement.requestFullscreen();
        } else if (document.documentElement.mozRequestFullScreen) {
          document.documentElement.mozRequestFullScreen();
        } else if (document.documentElement.webkitRequestFullscreen) {
          document.documentElement.webkitRequestFullscreen();
        } else if (document.documentElement.msRequestFullscreen) {
          document.documentElement.msRequestFullscreen();
        }
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
      }
    }

    // Three.js Animation
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ alpha: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    document.getElementById('three-canvas').appendChild(renderer.domElement);

    const geometry = new THREE.TorusKnotGeometry(10, 3, 100, 16);
    const material = new THREE.MeshBasicMaterial({
      color: 0x6366f1,
      wireframe: true,
      transparent: true,
      opacity: 0.1
    });
    const torusKnot = new THREE.Mesh(geometry, material);
    scene.add(torusKnot);

    camera.position.z = 30;

    function animate() {
      requestAnimationFrame(animate);
      torusKnot.rotation.x += 0.01;
      torusKnot.rotation.y += 0.01;
      renderer.render(scene, camera);
    }

    // Handle window resize
    window.addEventListener('resize', () => {
      camera.aspect = window.innerWidth / window.innerHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(window.innerWidth, window.innerHeight);
    });

    animate();
  </script>
</body>
</html>
