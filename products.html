<!DOCTYPE html>
<html lang="en">
<head>
  <meta name="description" content="Xerv delivers next-gen AI solutions for social media and digital transformation. Discover NeTuArk and RoBERTo.">
<meta name="robots" content="index, follow">
<meta name="author" content="Xerv">
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Xerv - Our Products</title>
  <!-- Modern fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=DM+Sans:wght@400;500;700&display=swap" rel="stylesheet">
  <!-- Tailwind CSS -->
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <!-- Three.js -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
  <style>
    :root {
      --primary: #A3E635;
      --primary-dark: #84CC16;
      --secondary: #22C55E;
      --dark: #111827;
      --light: #f5f3ff;
    }

    body {
      font-family: 'DM Sans', sans-serif;
      background-color: #080808;
      color: #ffffff;
      user-select: none;
      overflow-x: hidden;
    }

    .heading-font {
      font-family: 'Space Grotesk', sans-serif;
      font-weight: 600;
    }

    /* Glassmorphism card effect */
    .glass-card {
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 1rem;
      transition: all 0.4s ease;
    }

    .glass-card:hover {
      transform: translateY(-10px);
      background: rgba(255, 255, 255, 0.1);
      box-shadow: 0 10px 30px rgba(99, 102, 241, 0.2);
    }

    /* Gradient text */
    .gradient-text {
      background: linear-gradient(90deg, #A3E635, #22C55E, #10B981);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-size: 300% 100%;
      animation: gradient-shift 8s ease infinite;
    }

    @keyframes gradient-shift {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }

    /* 3D canvas */
    #three-canvas {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100vh;
      z-index: -1;
    }

    /* Navigation Menu */
    .nav-link {
      position: relative;
      padding: 0.5rem 0;
      margin: 0 1rem;
      transition: all 0.3s ease;
    }

    .nav-link::after {
      content: '';
      position: absolute;
      width: 0;
      height: 2px;
      bottom: 0;
      left: 0;
      background-color: var(--primary);
      transition: width 0.3s ease;
    }

    .nav-link:hover::after {
      width: 100%;
    }

    /* Mobile menu */
    .mobile-menu {
      position: fixed;
      top: 0;
      right: -100%;
      width: 80%;
      height: 100%;
      background: rgba(0, 0, 0, 0.95);
      backdrop-filter: blur(10px);
      transition: right 0.4s cubic-bezier(0.77, 0, 0.175, 1);
      z-index: 20;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }

    .mobile-menu.open {
      right: 0;
    }

    /* Overlay for mobile menu */
    .menu-overlay {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 15;
    }

    .menu-overlay.show {
      display: block;
    }

    /* Product number badge */
    .number-badge {
      background: linear-gradient(135deg, #f59e0b 0%, #fb923c 100%);
      box-shadow: 0 4px 10px rgba(245, 158, 11, 0.3);
    }

    /* Product card hover effects */
    .product-card {
      transition: all 0.4s ease;
      overflow: hidden;
    }

    .product-card:hover {
      transform: translateY(-10px) scale(1.02);
    }

    .product-card::before {
      content: '';
      position: absolute;
      top: -100%;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, rgba(99, 102, 241, 0.3) 0%, rgba(99, 102, 241, 0) 100%);
      transition: all 0.6s ease;
      z-index: -1;
    }

    .product-card:hover::before {
      top: 0;
      left: 0;
    }
  </style>
</head>
<body class="min-h-screen" oncontextmenu="return false" oncopy="return false" onpaste="return false">
  <!-- Three.js Canvas -->
  <div id="three-canvas"></div>

  <!-- Navigation Menu -->
  <nav class="container mx-auto px-6 py-6 relative z-10">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-3">
        <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
          <img src="Screenshot_20250104-002946-removebg-preview.png" alt="Zeta Technologies AI Platform Overview">
        </div>
        <span class="heading-font text-2xl gradient-text">Zeta Technologies</span>
      </div>

      <!-- Hamburger Menu for Mobile -->
      <div class="md:hidden flex items-center">
        <div class="hamburger text-2xl text-white" onclick="toggleMenu()">
          <i class="fas fa-bars"></i>
        </div>
      </div>

      <!-- Desktop Navigation (visible on medium+ screens) -->
      <div class="hidden md:flex items-center space-x-4">
        <a href="index.html" class="nav-link text-gray-300 hover:text-white">Home</a>
                <a href="products.html" class="nav-link text-gray-300 hover:text-white">Products</a>
                <a href="services.html" class="nav-link text-gray-300 hover:text-white">Services</a>
                <a href="about.html" class="nav-link text-gray-300 hover:text-white">About Us</a>
                <a href="careers.html" class="nav-link text-gray-300 hover:text-white">Careers</a>
                <a href="https://ko-fi.com/ZetaTech" target="_blank" class="ml-2 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-4 py-2 rounded-lg transition duration-300 flex items-center space-x-2">
                  <i class="fas fa-mug-hot"></i>
                  <span class="hidden lg:inline">Support Us</span>
                </a>
                <button onclick="toggleFullScreen()" class="ml-4 bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition duration-300 flex items-center space-x-2">
                  <i class="fas fa-expand"></i>
                  <span class="hidden lg:inline">Fullscreen</span>
                </button>
      </div>
    </div>
  </nav>

  <!-- Mobile Menu -->
  <div id="menuOverlay" class="menu-overlay" onclick="toggleMenu()"></div>
  <div id="mobileMenu" class="mobile-menu">
    <div class="absolute top-6 right-6 text-2xl text-white" onclick="toggleMenu()">
      <i class="fas fa-times"></i>
    </div>
    <div class="flex flex-col items-center space-y-8">
      <a href="index.html" class="nav-link text-gray-300 hover:text-white">Home</a>
      <a href="products.html" class="nav-link text-gray-300 hover:text-white">Products</a>
      <a href="services.html" class="nav-link text-gray-300 hover:text-white">Services</a>
      <a href="about.html" class="nav-link text-gray-300 hover:text-white">About Us</a>
      <a href="careers.html" class="nav-link text-gray-300 hover:text-white">Careers</a>
      <a href="https://ko-fi.com/ZetaTech" target="_blank" class="mt-6 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-6 py-3 rounded-lg transition duration-300 flex items-center space-x-2">
        <i class="fas fa-mug-hot"></i>
        <span>Support Us</span>
      </a>
      <button onclick="toggleFullScreen()" class="mt-6 bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg transition duration-300 flex items-center space-x-2">
        <i class="fas fa-expand"></i>
        <span>Fullscreen</span>
      </button>
    </div>
  </div>

  <!-- Main Content -->
  <main class="container mx-auto px-6 py-12 relative z-10">
    <header class="mb-16 text-center">
      <h1 class="heading-font text-5xl md:text-6xl gradient-text mb-4">Our Products</h1>
      <p class="text-gray-400 text-xl">Discover innovative digital and AI solutions</p>
    </header>

    <!-- Digital Section: NeTuArk -->
    <section class="mb-20">
      <div class="text-center mb-12">
        <span class="inline-block px-6 py-2 rounded-full bg-indigo-900 bg-opacity-30 text-indigo-300 border border-indigo-700 mb-4">DIGITAL</span>
        <div class="text-xl text-gray-400 mt-2">Social Media</div>
      </div>
      <div class="max-w-3xl mx-auto">
        <div class="relative">
          <div class="absolute -top-4 -left-4 md:-left-2 number-badge w-10 h-10 rounded-full flex items-center justify-center text-white font-bold z-10">1</div>
          <div class="glass-card p-8 relative">
            <div class="flex flex-col md:flex-row items-center">
              <div class="mb-6 md:mb-0 md:mr-8 flex-shrink-0">
                <div class="w-24 h-24 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center">
                  <img src="Screenshot_20250102-100517-removebg-preview.png" alt="NeTuArk Logo">
                </div>
              </div>
              <div>
                <h3 class="heading-font text-2xl font-bold text-white mb-2">NeTuArk</h3>
                <p class="text-gray-400 text-lg">Upcoming revolutionary social media platform designed to redefine online connections in a secure, user-centric environment.</p>
                <div class="mt-4 flex space-x-4">
                  <span class="px-3 py-1 rounded-full bg-indigo-900 bg-opacity-30 text-indigo-300 text-sm">Coming Soon</span>
                  <span class="px-3 py-1 rounded-full bg-purple-900 bg-opacity-30 text-purple-300 text-sm">Social Media</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section class="mb-20">
      <div class="text-center mb-12">
        <span class="inline-block px-6 py-2 rounded-full bg-indigo-900 bg-opacity-30 text-indigo-300 border border-indigo-700 mb-4">Communication</span>
        <div class="text-xl text-gray-400 mt-2">Collaboration</div>
      </div>
      <div class="max-w-3xl mx-auto">
        <div class="relative">
          <div class="absolute -top-4 -left-4 md:-left-2 number-badge w-10 h-10 rounded-full flex items-center justify-center text-white font-bold z-10">4</div>
          <div class="glass-card p-8 relative">
            <div class="flex flex-col md:flex-row items-center">
              <div class="mb-6 md:mb-0 md:mr-8 flex-shrink-0">
                <div class="w-24 h-24 rounded-full bg-gradient-to-br from-yellow-500 to-orange-600 flex items-center justify-center">
                  <i class="fas fa-video text-white text-3xl"></i>
                </div>
              </div>
              <div>
                <h3 class="heading-font text-2xl font-bold text-white mb-2">TheMeet</h3>
                <p class="text-gray-400 text-lg">Upcoming web application for seamless online meetings, calendar integrations, and collaborative tools.</p>
                <div class="mt-4 flex space-x-4">
                  <span class="px-3 py-1 rounded-full bg-yellow-900 bg-opacity-30 text-yellow-300 text-sm">Productivity</span>
                  <span class="px-3 py-1 rounded-full bg-indigo-900 bg-opacity-30 text-indigo-300 text-sm">Coming Soon</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- AI Section: RoBERTo & RoBERTo – Physics FineTuned -->
    <section class="mb-20">
      <div class="text-center mb-12">
        <span class="inline-block px-6 py-2 rounded-full bg-indigo-900 bg-opacity-30 text-indigo-300 border border-indigo-700 mb-4">AI</span>
        <div class="text-xl text-gray-400 mt-2">Artificial Intelligence Models</div>
      </div>
      <div class="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto">
        <!-- RoBERTo Card -->
        <div class="relative cursor-pointer" onclick="window.location.href='https://huggingface.co/ZetaTechnologies/RoBERTo';">
          <div class="absolute -top-4 -left-4 md:-left-2 number-badge w-10 h-10 rounded-full flex items-center justify-center text-white font-bold z-10">2</div>
          <div class="glass-card p-8 h-full product-card">
            <div class="flex flex-col items-center md:items-start">
              <div class="mb-6 w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-700 rounded-lg flex items-center justify-center">
                <i class="fas fa-robot text-white text-3xl"></i>
              </div>
              <h3 class="heading-font text-2xl font-bold text-white mb-2">RoBERTo</h3>
              <p class="text-gray-400 text-lg text-center md:text-left">Advanced BERT-based AI text classifier model designed for precise language understanding and classification.</p>
              <div class="mt-6 flex space-x-4">
                <span class="px-3 py-1 rounded-full bg-blue-900 bg-opacity-30 text-blue-300 text-sm">NLP</span>
                <span class="px-3 py-1 rounded-full bg-green-900 bg-opacity-30 text-green-300 text-sm">Text Classification</span>
              </div>
              <div class="mt-4">
                <a href="https://huggingface.co/ZetaTechnologies/RoBERTo" class="text-indigo-400 hover:text-indigo-300 transition inline-flex items-center space-x-1">
                  <span>Explore</span>
                  <i class="fas fa-external-link-alt text-xs"></i>
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- RoBERTo Physics FineTuned Card -->
        <div class="relative cursor-pointer" onclick="window.location.href='https://huggingface.co/ZetaTechnologies/RoBERTo-physics-v1-finetuned';">
          <div class="absolute -top-4 -left-4 md:-left-2 number-badge w-10 h-10 rounded-full flex items-center justify-center text-white font-bold z-10">3</div>
          <div class="glass-card p-8 h-full product-card">
            <div class="flex flex-col items-center md:items-start">
              <div class="mb-6 w-20 h-20 bg-gradient-to-br from-green-500 to-green-700 rounded-lg flex items-center justify-center">
                <i class="fas fa-atom text-white text-3xl"></i>
              </div>
              <h3 class="heading-font text-2xl font-bold text-white mb-2">RoBERTo – Physics FineTuned</h3>
              <p class="text-gray-400 text-lg text-center md:text-left">Specialized variant of RoBERTo meticulously trained on physics datasets for domain-specific applications.</p>
              <div class="mt-6 flex space-x-4">
                <span class="px-3 py-1 rounded-full bg-blue-900 bg-opacity-30 text-blue-300 text-sm">NLP</span>
                <span class="px-3 py-1 rounded-full bg-green-900 bg-opacity-30 text-green-300 text-sm">Text Classification</span>
              </div>
              <div class="mt-4">
                <a href="https://huggingface.co/ZetaTechnologies/RoBERTo-physics-v1-finetuned" class="text-indigo-400 hover:text-indigo-300 transition inline-flex items-center space-x-1">
                  <span>Explore</span>
                  <i class="fas fa-external-link-alt text-xs"></i>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="mt-32 pb-12">
      <div class="max-w-4xl mx-auto glass-card p-8">
        <div class="flex flex-col md:flex-row justify-between items-center">
          <div class="mb-8 md:mb-0">
            <div class="flex items-center space-x-3 mb-4">
              <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                <img src="Screenshot_20250104-002946-removebg-preview.png" alt="Zeta Technologies AI Platform Overview">
              </div>
              <span class="heading-font text-xl gradient-text">Zeta Technologies</span>
            </div>
            <p class="text-gray-400">Innovation Without Compromise</p>
          </div>
          <div class="flex space-x-6">
            <a href="https://x.com/TechAtZeta?t=f5UsigfHtK2gUjF5zQhpPQ&s=09" target="_blank" class="text-gray-400 hover:text-white transition">
              <i class="fab fa-twitter text-xl"></i>
            </a>
            <a href="https://web.facebook.com/profile.php?id=61576226673602" target="_blank" class="text-gray-400 hover:text-white transition">
              <i class="fab fa-facebook text-xl"></i>
            </a>
            <a href="https://www.linkedin.com/company/phase-official/" target="_blank" class="text-gray-400 hover:text-white transition">
              <i class="fab fa-linkedin text-xl"></i>
            </a>
            <a href="https://www.instagram.com/zetatechhq/" target="_blank" class="text-gray-400 hover:text-white transition">
              <i class="fab fa-instagram text-xl"></i>
            </a>
            <a href="https://ko-fi.com/ZetaTech" target="_blank" class="text-blue-400 hover:text-blue-300 transition">
              <i class="fas fa-mug-hot text-xl"></i>
            </a>
          </div>
        </div>
        <div class="mt-8 pt-8 border-t border-gray-800 text-center text-gray-500">
          &copy; 2025 Zeta Technologies. All rights reserved.
        </div>
      </div>
    </footer>
  </main>

  <script>
    // Three.js setup (same as homepage)
    function initThreeJS() {
      const scene = new THREE.Scene();
      const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
      camera.position.z = 5;
      const renderer = new THREE.WebGLRenderer({ alpha: true });
      renderer.setSize(window.innerWidth, window.innerHeight);
      renderer.setClearColor(0x000000, 0);
      document.getElementById('three-canvas').appendChild(renderer.domElement);

      // Create network nodes
      const nodes = [];
      const nodeCount = 50;
      const nodeGeometry = new THREE.SphereGeometry(0.05, 16, 16);
      for (let i = 0; i < nodeCount; i++) {
        const material = new THREE.MeshBasicMaterial({
          color: new THREE.Color(0.5 + 0.5 * Math.random(), 0.5 + 0.5 * Math.random(), 0.9 + 0.1 * Math.random())
        });
        const node = new THREE.Mesh(nodeGeometry, material);
        const radius = 4;
        const theta = Math.random() * Math.PI * 2;
        const phi = Math.random() * Math.PI;
        node.position.x = radius * Math.sin(phi) * Math.cos(theta);
        node.position.y = radius * Math.sin(phi) * Math.sin(theta);
        node.position.z = radius * Math.cos(phi);
        node.userData = {
          velocity: new THREE.Vector3((Math.random() - 0.5) * 0.01, (Math.random() - 0.5) * 0.01, (Math.random() - 0.5) * 0.01)
        };
        scene.add(node);
        nodes.push(node);
      }

      // Create connections
      const connections = [];
      const lineMaterial = new THREE.LineBasicMaterial({
        color: 0x6366f1,
        transparent: true,
        opacity: 0.4
      });
      for (let i = 0; i < nodeCount; i++) {
        const connectCount = 2 + Math.floor(Math.random() * 2);
        for (let j = 0; j < connectCount; j++) {
          if (i !== j) {
            const lineGeometry = new THREE.BufferGeometry().setFromPoints([ nodes[i].position, nodes[j].position ]);
            const line = new THREE.Line(lineGeometry, lineMaterial);
            scene.add(line);
            connections.push({ line: line, from: i, to: j });
          }
        }
      }

      function animate() {
        requestAnimationFrame(animate);
        nodes.forEach(node => {
          node.position.add(node.userData.velocity);
          const distance = node.position.length();
          const maxRadius = 4;
          if (distance > maxRadius) {
            node.position.setLength(maxRadius);
            node.userData.velocity.reflect(node.position.clone().normalize());
          }
          node.rotation.x += 0.01;
          node.rotation.y += 0.01;
        });
        connections.forEach(connection => {
          const points = [ nodes[connection.from].position.clone(), nodes[connection.to].position.clone() ];
          connection.line.geometry.setFromPoints(points);
          connection.line.geometry.attributes.position.needsUpdate = true;
        });
        scene.rotation.y += 0.001;
        scene.rotation.x += 0.0005;
        renderer.render(scene, camera);
      }
      window.addEventListener('resize', () => {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
      });
      animate();
    }

    document.addEventListener('DOMContentLoaded', initThreeJS);

    // Mobile Menu Toggle
    function toggleMenu() {
      document.getElementById("mobileMenu").classList.toggle("open");
      document.getElementById("menuOverlay").classList.toggle("show");
    }

    // Fullscreen toggle
    function toggleFullScreen() {
      if (!document.fullscreenElement && !document.mozFullScreenElement && !document.webkitFullscreenElement) {
        if (document.documentElement.requestFullscreen) {
          document.documentElement.requestFullscreen();
        } else if (document.documentElement.mozRequestFullScreen) {
          document.documentElement.mozRequestFullScreen();
        } else if (document.documentElement.webkitRequestFullscreen) {
          document.documentElement.webkitRequestFullscreen();
        } else if (document.documentElement.msRequestFullscreen) {
          document.documentElement.msRequestFullscreen();
        }
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
      }
    }

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function(e) {
        e.preventDefault();
        const targetId = this.getAttribute('href');
        const targetElement = document.querySelector(targetId);
        if (targetElement) {
          window.scrollTo({ top: targetElement.offsetTop, behavior: 'smooth' });
          document.getElementById("mobileMenu").classList.remove("open");
          document.getElementById("menuOverlay").classList.remove("show");
        }
      });
    });

    // Disable text selection, copy, and paste
    document.body.oncopy = document.body.onpaste = function() { return false; };
  </script>
</body>
          </html
